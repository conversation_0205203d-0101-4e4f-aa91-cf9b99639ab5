defmodule Drops.SQL.SqliteOptimizationTest do
  use Test.RelationCase, async: false

  alias Drops.SQL.Database

  describe "SQLite query optimization" do
    @tag relations: [:users], adapter: :sqlite
    test "still extracts check constraints correctly after optimization", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # This test verifies that the optimization doesn't break functionality
      assert %Database.Table{} = table
      assert table.name == :users
      assert length(table.columns) > 0

      # Verify that columns still have their metadata including check constraints
      for column <- table.columns do
        assert is_list(column.meta.check_constraints)
        # Verify other metadata is still present
        assert is_boolean(column.meta.primary_key)
        assert is_boolean(column.meta.nullable)
        assert is_boolean(column.meta.foreign_key)
      end
    end

    @tag relations: [:users], adapter: :sqlite
    test "table introspection works correctly", %{repo: repo} do
      {:ok, table} = Database.table("users", repo)

      # Verify basic table structure
      assert table.adapter == :sqlite
      assert is_list(table.columns)
      assert is_list(table.foreign_keys)
      assert is_list(table.indices)

      # Verify we have expected columns (based on users migration)
      column_names = Enum.map(table.columns, & &1.name)
      assert :id in column_names
      assert :email in column_names
      assert :active in column_names
    end
  end
end
